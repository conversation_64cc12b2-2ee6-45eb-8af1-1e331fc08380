"use server";

import { db } from "@/lib/db";
import { getEffectiveUserId } from "@/lib/auth";
import * as XLSX from "xlsx";

interface ImportSummary {
  purchasesCreated: number;
  suppliersCreated: number;
  warehousesCreated: number;
  errors: string[];
}

interface ImportResult {
  success?: string;
  error?: string;
  summary: ImportSummary;
}

// Utility function to sanitize string inputs
const sanitizeString = (value: any): string => {
  if (value === null || value === undefined || value === "") return "";
  return String(value).trim().substring(0, 255);
};

// Utility function to sanitize number inputs
const sanitizeNumber = (value: any): number => {
  if (value === null || value === undefined || value === "") return 0;
  const num = Number(value);
  return isNaN(num) ? 0 : Math.max(0, num);
};

// Utility function to sanitize date inputs
const sanitizeDate = (value: any): Date => {
  if (value === null || value === undefined || value === "") {
    return new Date();
  }
  
  // Handle Excel date serial numbers
  if (typeof value === "number") {
    // Excel date serial number (days since 1900-01-01)
    const excelEpoch = new Date(1900, 0, 1);
    const date = new Date(excelEpoch.getTime() + (value - 1) * 24 * 60 * 60 * 1000);
    return isNaN(date.getTime()) ? new Date() : date;
  }
  
  // Handle string dates
  if (typeof value === "string") {
    const date = new Date(value);
    return isNaN(date.getTime()) ? new Date() : date;
  }
  
  // Handle Date objects
  if (value instanceof Date) {
    return isNaN(value.getTime()) ? new Date() : value;
  }
  
  return new Date();
};

// Main import function for purchases
export const importPurchases = async (
  arrayBuffer: ArrayBuffer
): Promise<ImportResult> => {
  try {
    console.log("[IMPORT] Starting purchase import process");

    // Get effective user ID
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return {
        error: "Tidak terautentikasi",
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: ["User tidak terautentikasi"],
        },
      };
    }

    // Parse Excel file
    const workbook = XLSX.read(arrayBuffer, { type: "array" });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    console.log(`[IMPORT] Parsed ${data.length} rows from Excel`);

    if (data.length < 2) {
      return {
        error: "File Excel kosong atau tidak memiliki data",
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: ["File tidak memiliki data yang valid"],
        },
      };
    }

    // Get headers from first row
    const headers = data[0] as string[];
    const dataRows = data.slice(1);

    // Convert to objects
    const purchaseData = dataRows
      .map((row: any[], index: number) => {
        const obj: any = {};
        headers.forEach((header, i) => {
          obj[header] = row[i];
        });
        obj._rowIndex = index + 2; // +2 because we start from row 2 (after header)
        return obj;
      })
      .filter((row) => {
        // Filter out empty rows
        return (
          sanitizeString(row["Tanggal Pembelian"]) &&
          sanitizeString(row["Nama Produk"]) &&
          sanitizeNumber(row["Quantity"]) > 0 &&
          sanitizeNumber(row["Harga Beli"]) > 0
        );
      });

    console.log(`[IMPORT] Filtered to ${purchaseData.length} valid purchase rows`);

    if (purchaseData.length === 0) {
      return {
        error: "Tidak ada data pembelian yang valid ditemukan",
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: ["Tidak ada baris data yang memenuhi kriteria minimum"],
        },
      };
    }

    if (purchaseData.length > 500) {
      return {
        error: "Terlalu banyak data. Maksimal 500 transaksi per import",
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: ["Jumlah data melebihi batas maksimal 500 transaksi"],
        },
      };
    }

    // Process import in smaller batches
    const BATCH_SIZE = 10;
    let totalPurchasesCreated = 0;
    let totalSuppliersCreated = 0;
    let totalWarehousesCreated = 0;
    const allErrors: string[] = [];

    // Process data in batches
    for (
      let batchStart = 0;
      batchStart < purchaseData.length;
      batchStart += BATCH_SIZE
    ) {
      const batchEnd = Math.min(batchStart + BATCH_SIZE, purchaseData.length);
      const batch = purchaseData.slice(batchStart, batchEnd);

      console.log(
        `[IMPORT] Processing batch ${Math.floor(batchStart / BATCH_SIZE) + 1}/${Math.ceil(purchaseData.length / BATCH_SIZE)} (${batch.length} purchases)`
      );

      try {
        const batchResult = await db.$transaction(
          async (tx) => {
            let purchasesCreated = 0;
            let suppliersCreated = 0;
            let warehousesCreated = 0;
            const errors: string[] = [];

            for (const row of batch) {
              try {
                const rowIndex = row._rowIndex;

                // Validate and sanitize required fields
                const purchaseDate = sanitizeDate(row["Tanggal Pembelian"]);
                const productName = sanitizeString(row["Nama Produk"]);
                const quantity = sanitizeNumber(row["Quantity"]);
                const costPrice = sanitizeNumber(row["Harga Beli"]);

                if (!productName) {
                  errors.push(`Baris ${rowIndex}: Nama Produk tidak boleh kosong`);
                  continue;
                }

                if (quantity <= 0) {
                  errors.push(`Baris ${rowIndex}: Quantity harus lebih dari 0`);
                  continue;
                }

                if (costPrice <= 0) {
                  errors.push(`Baris ${rowIndex}: Harga Beli harus lebih dari 0`);
                  continue;
                }

                // Find existing product
                const existingProduct = await tx.product.findFirst({
                  where: {
                    name: productName,
                    userId: effectiveUserId,
                  },
                });

                if (!existingProduct) {
                  errors.push(`Baris ${rowIndex}: Produk "${productName}" tidak ditemukan di sistem`);
                  continue;
                }

                // Handle supplier
                let supplierId: string | null = null;
                const supplierName = sanitizeString(row["Supplier"]);
                if (supplierName) {
                  let supplier = await tx.supplier.findFirst({
                    where: {
                      name: supplierName,
                      userId: effectiveUserId,
                    },
                  });

                  if (!supplier) {
                    supplier = await tx.supplier.create({
                      data: {
                        name: supplierName,
                        userId: effectiveUserId,
                      },
                    });
                    suppliersCreated++;
                  }
                  supplierId = supplier.id;
                }

                // Handle warehouse
                let warehouseId: string | null = null;
                const warehouseName = sanitizeString(row["Gudang"]);
                if (warehouseName) {
                  let warehouse = await tx.warehouse.findFirst({
                    where: {
                      name: warehouseName,
                      userId: effectiveUserId,
                    },
                  });

                  if (!warehouse) {
                    warehouse = await tx.warehouse.create({
                      data: {
                        name: warehouseName,
                        userId: effectiveUserId,
                      },
                    });
                    warehousesCreated++;
                  }
                  warehouseId = warehouse.id;
                }

                // Calculate discounts and totals
                const discountPercentage = sanitizeNumber(row["Diskon (%)"]);
                const discountAmount = sanitizeNumber(row["Diskon (Rp)"]);
                const taxPercentage = sanitizeNumber(row["PPN (%)"]);

                const subtotal = quantity * costPrice;
                const finalDiscountAmount = discountAmount > 0 ? discountAmount : (subtotal * discountPercentage / 100);
                const afterDiscount = subtotal - finalDiscountAmount;
                const taxAmount = afterDiscount * taxPercentage / 100;
                const totalAmount = afterDiscount + taxAmount;

                // Generate transaction number
                const currentYear = new Date().getFullYear().toString().slice(-2);
                const lastPurchase = await tx.purchase.findFirst({
                  where: { userId: effectiveUserId },
                  orderBy: { createdAt: "desc" },
                });

                let nextNumber = 1;
                if (lastPurchase?.transactionNumber) {
                  const match = lastPurchase.transactionNumber.match(/BELI-\d{2}B(\d{6})/);
                  if (match) {
                    nextNumber = parseInt(match[1]) + 1;
                  }
                }

                const transactionNumber = `BELI-${currentYear}B${nextNumber.toString().padStart(6, "0")}`;

                // Create purchase
                const newPurchase = await tx.purchase.create({
                  data: {
                    purchaseDate,
                    totalAmount,
                    transactionNumber,
                    invoiceRef: sanitizeString(row["No. Invoice"]) || null,
                    memo: sanitizeString(row["Memo"]) || null,
                    userId: effectiveUserId,
                    supplierId,
                    warehouseId,
                    isDraft: false,
                  },
                });

                // Create purchase item
                await tx.purchaseItem.create({
                  data: {
                    quantity,
                    costAtPurchase: costPrice,
                    unit: sanitizeString(row["Satuan"]) || "Pcs",
                    discountPercentage: discountPercentage > 0 ? discountPercentage : null,
                    discountAmount: finalDiscountAmount > 0 ? finalDiscountAmount : null,
                    tax: taxPercentage > 0 ? `${taxPercentage}%` : null,
                    purchaseId: newPurchase.id,
                    productId: existingProduct.id,
                  },
                });

                // Update product stock
                await tx.product.update({
                  where: { id: existingProduct.id },
                  data: {
                    stock: {
                      increment: quantity,
                    },
                  },
                });

                purchasesCreated++;
              } catch (error) {
                console.error(`[IMPORT] Error processing row ${row._rowIndex}:`, error);
                errors.push(
                  `Baris ${row._rowIndex}: ${error instanceof Error ? error.message : "Error tidak diketahui"}`
                );
              }
            }

            return {
              purchasesCreated,
              suppliersCreated,
              warehousesCreated,
              errors,
            };
          },
          { timeout: 30000 }
        );

        // Accumulate results
        totalPurchasesCreated += batchResult.purchasesCreated;
        totalSuppliersCreated += batchResult.suppliersCreated;
        totalWarehousesCreated += batchResult.warehousesCreated;
        allErrors.push(...batchResult.errors);

        console.log(
          `[IMPORT] Batch completed: ${batchResult.purchasesCreated} purchases created`
        );
      } catch (error) {
        console.error(`[IMPORT] Batch failed:`, error);
        allErrors.push(
          `Batch ${Math.floor(batchStart / BATCH_SIZE) + 1} gagal: ${error instanceof Error ? error.message : "Unknown error"}`
        );
      }
    }

    // Return final results
    const finalResult = {
      purchasesCreated: totalPurchasesCreated,
      suppliersCreated: totalSuppliersCreated,
      warehousesCreated: totalWarehousesCreated,
      errors: allErrors,
    };

    if (allErrors.length > 0 && totalPurchasesCreated === 0) {
      return {
        error: "Import gagal",
        summary: finalResult,
      };
    }

    return {
      success: `Import berhasil! ${totalPurchasesCreated} pembelian berhasil diimpor.`,
      summary: finalResult,
    };
  } catch (error) {
    console.error("Import error:", error);
    return {
      error: "Gagal memproses file import",
      summary: {
        purchasesCreated: 0,
        suppliersCreated: 0,
        warehousesCreated: 0,
        errors: [error instanceof Error ? error.message : "Unknown error"],
      },
    };
  }
};
